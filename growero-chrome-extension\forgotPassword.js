document.addEventListener("DOMContentLoaded", function () {
  const form = document.getElementById("forgotPasswordForm");
  const emailInput = document.getElementById("forgotPasswordEmail");
  const sendResetButton = document.getElementById("sendResetButton");
  const successState = document.getElementById("successState");
  const backToLogin = document.getElementById("backToLogin");
  const backToLoginButton = document.getElementById("backToLoginButton");
  const errorMessage = document.getElementById("errorMessage");

  const BASE_URL = "https://api.growero.io/api/";
  //const BASE_URL = "http://localhost:9090/api/"
  function showError(message) {
    if (errorMessage) {
      errorMessage.textContent = message;
      errorMessage.classList.remove("hidden");

      // Auto-hide error after 5 seconds
      setTimeout(() => {
        errorMessage.classList.add('hidden');
      }, 5000);
    }
  }

  function clearError() {
    if (errorMessage) {
      errorMessage.textContent = "";
      errorMessage.classList.add("hidden");
    }

    // Remove error styling from email input
    emailInput?.classList.remove("error");
  }

  function setLoading(isLoading) {
    const buttonText = sendResetButton.querySelector("span");
    sendResetButton.disabled = isLoading;
    buttonText.textContent = isLoading
      ? "Sending Email..."
      : "Send Reset Email";
  }

  function showSuccessState(email) {
    form.classList.add("hidden");
    successState.classList.remove("hidden");
    document.getElementById("pageTitle").textContent = "Email Sent";
    document.getElementById("pageSubtitle").textContent =
      "Check your inbox for password reset instructions";
  }

  form.addEventListener("submit", async (e) => {
    e.preventDefault();
    clearError();

    const email = emailInput.value.trim();
    if (!email) {
      showError("Please enter your email address");
      emailInput.classList.add("error");
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      showError("Please enter a valid email address");
      emailInput.classList.add("error");
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`${BASE_URL}userprofile/forgotPassword`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to send reset password email");
      }

      showSuccessState(email);
    } catch (error) {
      console.error("Forgot password error:", error);

      let errorMessage = "Failed to send reset password email. Please try again.";
      if (error.message.includes("Failed to fetch")) {
        errorMessage = "Connection error. Please check your internet connection and try again.";
      } else if (error.message.includes("404")) {
        errorMessage = "Email address not found. Please check your email or create a new account.";
      } else if (error.message.includes("500")) {
        errorMessage = "Server error. Please try again later.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      showError(errorMessage);
      emailInput.classList.add("error");
    } finally {
      setLoading(false);
    }
  });

  [backToLogin, backToLoginButton].forEach((button) => {
    button?.addEventListener("click", () => {
      window.location.href = "popup.html";
    });
  });

  // Clear error when user starts typing
  emailInput?.addEventListener("input", () => {
    clearError();
  });
});
