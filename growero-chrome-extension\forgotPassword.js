document.addEventListener("DOMContentLoaded", function () {
  const form = document.getElementById("forgotPasswordForm");
  const emailInput = document.getElementById("forgotPasswordEmail");
  const sendResetButton = document.getElementById("sendResetButton");
  const successState = document.getElementById("successState");
  const backToLogin = document.getElementById("backToLogin");
  const backToLoginButton = document.getElementById("backToLoginButton");
  const errorMessage = document.getElementById("errorMessage");

  const BASE_URL = "https://api.growero.io/api";// change??
  //const BASE_URL = "http://localhost:9090/api/"
  function showError(message) {
    errorMessage.textContent = message;
    errorMessage.classList.remove("hidden");
  }

  function clearError() {
    errorMessage.textContent = "";
    errorMessage.classList.add("hidden");
  }

  function setLoading(isLoading) {
    const buttonText = sendResetButton.querySelector("span");
    sendResetButton.disabled = isLoading;
    buttonText.textContent = isLoading
      ? "Sending Email..."
      : "Send Reset Email";
  }

  function showSuccessState(email) {
    form.classList.add("hidden");
    successState.classList.remove("hidden");
    document.getElementById("pageTitle").textContent = "Email Sent";
    document.getElementById("pageSubtitle").textContent =
      "Check your inbox for password reset instructions";
  }

  form.addEventListener("submit", async (e) => {
    e.preventDefault();
    clearError();

    const email = emailInput.value.trim();
    if (!email) {
      showError("Please enter your email address");
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`${BASE_URL}userprofile/forgotPassword`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to send reset password email");
      }

      showSuccessState(email);
    } catch (error) {
      console.error("Forgot password error:", error);
      showError(
        error.message ||
          "Failed to send reset password email. Please try again."
      );
    } finally {
      setLoading(false);
    }
  });

  [backToLogin, backToLoginButton].forEach((button) => {
    button?.addEventListener("click", () => {
      window.location.href = "popup.html";
    });
  });
});
