document.addEventListener("DOMContentLoaded", function () {
  const getTokenButton = document.getElementById("get-token");
  const logoutButton = document.getElementById("logout-button");
  const statusDiv = document.getElementById("status");

  function safeClosePopup() {
    try {
      window.close();
    } catch (error) {
      setTimeout(() => {
        window.open("", "_self").close();
      }, 0);
    }
  }

  function updateButtonState(button, loading = false) {
    if (!button) return;
    button.disabled = loading;
    const buttonText = button.querySelector("span");
    if (buttonText) {
      buttonText.textContent = loading
        ? "Processing..."
        : button.dataset.originalText || "Allow Access";
    }
    button.classList.toggle("loading", loading);
  }

  function updateStatus(message, isError = false) {
    if (statusDiv) {
      statusDiv.textContent = message;
      statusDiv.className = `status-message ${isError ? "error" : "success"}`;
    }
  }

  function handleCookieResponse(response) {
    try {
      if (!response) {
        updateStatus("Failed to get LinkedIn access. Please try again.", true);
        updateButtonState(getTokenButton, false);
        return;
      }

      // this is for API response success
      if (response.status === "success" && response.apiResponse) {
        updateStatus("Successfully connected to LinkedIn!");

        // this will store the tokens from API response
        chrome.storage.local.set(
          {
            isAuthenticated: true,
            liAtToken: response.apiResponse.li_at_cookie,
            jsessionidToken: response.apiResponse.csrf_token,
          },
          () => {
            if (chrome.runtime.lastError) {
              console.error("Storage error:", chrome.runtime.lastError);
              updateStatus("Error saving credentials. Please try again.", true);
            } else {
              updateStatus("Successfully authenticated with LinkedIn!");
              // success message briefly before closing
              setTimeout(safeClosePopup, 1500);
            }
          }
        );
      } else if (response.status === "error") {
        // specific error messages from background.js
        const errorMessage = response.message || "Unknown error occurred";
        updateStatus(`LinkedIn access failed: ${errorMessage}`, true);
        updateButtonState(getTokenButton, false);

        if (errorMessage.includes("Could not find li_at cookie")) {
          updateStatus("Please log in to LinkedIn first and try again.", true);
        }
      } else {
        updateStatus(
          "Failed to connect with LinkedIn. Please try again.",
          true
        );
        updateButtonState(getTokenButton, false);
      }
    } catch (error) {
      console.error("Error handling cookie response:", error);
      updateStatus("An unexpected error occurred. Please try again.", true);
      updateButtonState(getTokenButton, false);
    }
  }

  // logout ka event listener
  logoutButton?.addEventListener("click", async () => {
    updateButtonState(logoutButton, true);

    try {
      chrome.storage.local.clear(() => {
        if (chrome.runtime.lastError) {
          console.error("Error clearing storage:", chrome.runtime.lastError);
          updateStatus("Error during logout. Please try again.", true);
        } else {
          window.location.href = "popup.html";
        }
      });
    } catch (error) {
      console.error("Logout error:", error);
      updateButtonState(logoutButton, false);
      updateStatus("Failed to logout. Please try again.", true);
    }
  });

  getTokenButton?.addEventListener("click", function () {
    updateButtonState(getTokenButton, true);
    updateStatus("Checking LinkedIn connection...");

    // first verify we're on LinkedIn
    chrome.tabs.query(
      {
        active: true,
        currentWindow: true,
      },
      function (tabs) {
        const currentTab = tabs[0];
        const isLinkedIn = currentTab?.url?.includes("linkedin.com");

        if (!isLinkedIn) {
          updateStatus("Please open LinkedIn in another tab first", true);
          updateButtonState(getTokenButton, false);
          return;
        }

        // ab get cookies
        chrome.runtime.sendMessage(
          {
            action: "getCookies",
          },
          function (response) {
            if (chrome.runtime.lastError) {
              const errorMsg = chrome.runtime.lastError.message || JSON.stringify(chrome.runtime.lastError);
              console.error("Message error:", errorMsg);
              updateStatus(`Extension error: ${errorMsg}. Please try again.`, true);
              updateButtonState(getTokenButton, false);
              return;
            }
            
            if (!response) {
              console.error("No response received from background script");
              updateStatus("No response from extension. Please reload.", true);
              updateButtonState(getTokenButton, false);
              return;
            }
            
            handleCookieResponse(response);
          }
        );
      }
    );
  });

  // store original button text
  if (getTokenButton) {
    getTokenButton.dataset.originalText =
      getTokenButton.querySelector("span")?.textContent || "Allow Access";
  }

  if (logoutButton) {
    logoutButton.dataset.originalText =
      logoutButton.querySelector("span")?.textContent || "Sign Out";
  }
});
