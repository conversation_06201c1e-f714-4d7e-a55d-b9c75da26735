body {
  width: 480px;
  height: 600px;
  margin: 0;
  padding: 24px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
  background: linear-gradient(126deg,#8A51FF -9.26%,#6c87ff 79.86%);
  color: #2d3748;
  box-sizing: border-box;
  overflow: hidden; /* Prevent scrollbar */
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100%;
  overflow: hidden;
}

.auth-container {
  background: white;
  padding: 32px 40px;
  border-radius: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  height: 100%;
  max-height: 100%;
  overflow-y: auto; /* scrolling allow karega within container if needed */
  /* ye scrollbar hide karega */
  &::-webkit-scrollbar {
    display: none;
  }

  /* ye bhi scrollbar hide karega */
  -ms-overflow-style: none;
  scrollbar-width: none;
  display: flex;
  flex-direction: column;
}

.hidden {
  display: none;
}

.brand {
  text-align: center;
  margin-bottom: 20px;
}

.logo {
  width: 120px;
  height: auto;
  /* margin-top: 40px; */
  margin-bottom: 16px;
}

h1 {
  font-size: 28px;
  margin: 0 0 12px;
  color: #1a202c;
  font-weight: 800;
}

.subtitle {
  font-size: 16px;
  margin: 0;
  color: #718096;
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  transition: all 0.3s ease-in-out;
}

.form-container.hidden {
  display: none;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

label {
  font-size: 15px;
  font-weight: 500;
  color: #4a5568;
  margin-bottom: -8px;
}

input {
  width: 100%;
  padding: 14px 18px;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  background-color: white;
  color: #2d3748;
  outline: none;
  font-size: 15px;
  box-sizing: border-box;
  transition: all 0.2s ease;
}

input::placeholder {
  color: #a0aec0;
}

input:focus {
  border-color: #805ad5;
  box-shadow: 0 0 0 3px rgba(128, 90, 213, 0.1);
}

.action-button {
  width: 100%;
  padding: 14px;
  border: none;
  border-radius: 10px;
  background: linear-gradient(126deg,#8A51FF -9.26%,#39B2FF 79.86%);
  color: white;
  cursor: pointer;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: #6b46c1;
}

.action-button:active {
  transform: translateY(1px);
}

.bottom-text {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.bottom-text .link {
  color: #805ad5;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  padding: 4px;
}

.bottom-text .link:hover {
  text-decoration: underline;
}

/* Loading state */
.action-button.loading {
  position: relative;
  background-color: #6b46c1;
  cursor: not-allowed;
}

.action-button.loading span {
  opacity: 0.7;
}

/* Update the error styles */
input.error {
  border-color: #dc2626 !important;
  background-color: #fef2f2 !important;
  animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

/* Add shake animation */
@keyframes shake {
  10%, 90% {
    transform: translate3d(-1px, 0, 0);
  }
  20%, 80% {
    transform: translate3d(2px, 0, 0);
  }
  30%, 50%, 70% {
    transform: translate3d(-2px, 0, 0);
  }
  40%, 60% {
    transform: translate3d(2px, 0, 0);
  }
}

/* Remove error message styles since we're not using them */
.error-message {
  display: none;
}

/* loading state for button */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

input.error:focus {
  border-color: #e53e3e;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.action-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.status-message {
  text-align: center;
  color: #666;
  font-size: 14px;
  margin: 10px 0;
}

.separator {
  margin: 0 8px;
  color: #666;
}

.action-button.secondary {
  background-color: #e2e8f0;
  color: #4a5568;
  margin-top: 8px;
}

.action-button.secondary:hover {
  background-color: #cbd5e0;
}

.status-card {
  background-color: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.status-icon {
  font-size: 12px;
}

.status-title {
  font-weight: 600;
  color: #2d3748;
}

.action-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.button-divider {
  text-align: center;
  position: relative;
  margin: 8px 0;
}

.button-divider::before,
.button-divider::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 45%;
  height: 1px;
  background-color: #e2e8f0;
}

.button-divider::before {
  left: 0;
}

.button-divider::after {
  right: 0;
}

.button-divider span {
  background-color: white;
  padding: 0 10px;
  color: #718096;
  font-size: 14px;
}

.info-section {
  background-color: #f8fafc;
  border-radius: 12px;
  padding: 16px;
  margin: 16px 0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-icon {
  font-size: 16px;
}

.info-text {
  font-size: 14px;
  color: #4a5568;
}

.action-button.primary {
  background-color: #805ad5;
}

.action-button.primary:hover {
  background-color: #6b46c1;
}

.status {
  margin: 10px 0;
  padding: 8px;
  border-radius: 4px;
}

.status.error {
  background-color: #ffe6e6;
  color: #d32f2f;
}

.status.success {
  background-color: #e8f5e9;
  color: #2e7d32;
}

/* Add to your existing styles */
.success-icon {
  font-size: 64px;
  text-align: center;
  margin: 20px 0;
}

.success-text {
  color: #4b5563;
  font-size: 16px;
  text-align: center;
  margin: 16px 0;
  line-height: 1.5;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.text-center {
  text-align: center;
}

/* Animation for success state transition */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

#successState:not(.hidden) {
  animation: fadeIn 0.3s ease-in-out;
}

/* Add these styles */
.password-requirements {
  margin-top: 8px;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 8px;
  font-size: 14px;
}

.requirements-title {
  color: #4b5563;
  margin-bottom: 8px;
  font-weight: 500;
}

.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.requirements-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  margin-bottom: 4px;
  transition: color 0.2s ease;
}

.requirements-list li.met {
  color: #059669;
}

.check-icon {
  font-size: 12px;
  min-width: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Dark theme support */
html.theme--dark .password-requirements,
.dark-theme .password-requirements {
  background-color: rgba(255, 255, 255, 0.05);
}

html.theme--dark .requirements-title,
.dark-theme .requirements-title {
  color: #e5e7eb;
}

html.theme--dark .requirements-list li,
.dark-theme .requirements-list li {
  color: #9ca3af;
}

html.theme--dark .requirements-list li.met,
.dark-theme .requirements-list li.met {
  color: #34d399;
}
