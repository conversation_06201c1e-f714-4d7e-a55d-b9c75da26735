body {
  width: 480px;
  height: 600px;
  margin: 0;
  padding: 24px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #2d3748;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

/* Add subtle background pattern */
body::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100%;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.auth-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  padding: 36px 40px;
  border-radius: 24px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  height: 100%;
  max-height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Hide scrollbar but keep functionality */
.auth-container::-webkit-scrollbar {
  display: none;
}

.auth-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hidden {
  display: none;
}

.brand {
  text-align: center;
  margin-bottom: 28px;
  position: relative;
}

.logo {
  width: 140px;
  height: auto;
  margin-bottom: 20px;
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
}

.logo:hover {
  transform: scale(1.02);
}

h1 {
  font-size: 32px;
  margin: 0 0 8px;
  color: #1a202c;
  font-weight: 700;
  letter-spacing: -0.5px;
  line-height: 1.2;
}

.subtitle {
  font-size: 16px;
  margin: 0;
  color: #64748b;
  font-weight: 400;
  line-height: 1.4;
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  transition: all 0.3s ease-in-out;
}

.form-container.hidden {
  display: none;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: -8px;
  letter-spacing: 0.025em;
}

input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background-color: #ffffff;
  color: #1f2937;
  outline: none;
  font-size: 15px;
  box-sizing: border-box;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 400;
}

input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

input:focus {
  border-color: #6366f1;
  box-shadow:
    0 0 0 3px rgba(99, 102, 241, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

input:hover:not(:focus) {
  border-color: #d1d5db;
}

.action-button {
  width: 100%;
  padding: 16px 24px;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  cursor: pointer;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 4px 14px rgba(99, 102, 241, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.025em;
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.action-button:hover::before {
  left: 100%;
}

.action-button:hover {
  background: linear-gradient(135deg, #5b5bf6 0%, #7c3aed 100%);
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(99, 102, 241, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.15);
}

.action-button:active {
  transform: translateY(0);
  box-shadow:
    0 2px 8px rgba(99, 102, 241, 0.3),
    0 1px 2px rgba(0, 0, 0, 0.1);
}

.bottom-text {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: auto;
  padding-top: 20px;
  border-top: 1px solid rgba(226, 232, 240, 0.8);
}

.bottom-text .link {
  color: #6366f1;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 14px;
}

.bottom-text .link:hover {
  background-color: rgba(99, 102, 241, 0.1);
  color: #4f46e5;
  transform: translateY(-1px);
}

/* Loading state */
.action-button.loading {
  position: relative;
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
  cursor: not-allowed;
  pointer-events: none;
}

.action-button.loading::before {
  display: none;
}

.action-button.loading span {
  opacity: 0.8;
}

.action-button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced error styles */
input.error {
  border-color: #ef4444 !important;
  background-color: #fef2f2 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
  animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

/* Add shake animation */
@keyframes shake {
  10%, 90% {
    transform: translate3d(-1px, 0, 0);
  }
  20%, 80% {
    transform: translate3d(2px, 0, 0);
  }
  30%, 50%, 70% {
    transform: translate3d(-2px, 0, 0);
  }
  40%, 60% {
    transform: translate3d(2px, 0, 0);
  }
}

/* Remove error message styles since we're not using them */
.error-message {
  display: none;
}

/* loading state for button */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

input.error:focus {
  border-color: #dc2626 !important;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.15) !important;
}

.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Enhanced error message styles */
.error-message {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  margin: 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-message::before {
  content: '⚠️';
  font-size: 16px;
}

.error-message.hidden {
  display: none;
}

.status-message {
  text-align: center;
  color: #475569;
  font-size: 15px;
  margin: 0;
  line-height: 1.5;
  font-weight: 500;
}

.status-message.success {
  color: #059669;
  background-color: rgba(5, 150, 105, 0.1);
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid rgba(5, 150, 105, 0.2);
}

.status-message.error {
  color: #dc2626;
  background-color: rgba(220, 38, 38, 0.1);
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid rgba(220, 38, 38, 0.2);
}

.separator {
  margin: 0 8px;
  color: #666;
}

.action-button.secondary {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: #475569;
  margin-top: 8px;
  border: 1px solid #e2e8f0;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.05),
    0 1px 2px rgba(0, 0, 0, 0.1);
}

.action-button.secondary:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
  color: #334155;
  transform: translateY(-1px);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.15);
}

.action-button.secondary::before {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

.status-card {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.status-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #6366f1, #8b5cf6, #06b6d4);
  border-radius: 16px 16px 0 0;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.status-icon {
  font-size: 12px;
}

.status-title {
  font-weight: 600;
  color: #2d3748;
}

.action-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.button-divider {
  text-align: center;
  position: relative;
  margin: 8px 0;
}

.button-divider::before,
.button-divider::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 45%;
  height: 1px;
  background-color: #e2e8f0;
}

.button-divider::before {
  left: 0;
}

.button-divider::after {
  right: 0;
}

.button-divider span {
  background-color: white;
  padding: 0 10px;
  color: #718096;
  font-size: 14px;
}

.info-section {
  background-color: #f8fafc;
  border-radius: 12px;
  padding: 16px;
  margin: 16px 0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-icon {
  font-size: 16px;
}

.info-text {
  font-size: 14px;
  color: #4a5568;
}

.action-button.primary {
  background-color: #805ad5;
}

.action-button.primary:hover {
  background-color: #6b46c1;
}

.status {
  margin: 10px 0;
  padding: 8px;
  border-radius: 4px;
}

.status.error {
  background-color: #ffe6e6;
  color: #d32f2f;
}

.status.success {
  background-color: #e8f5e9;
  color: #2e7d32;
}

/* Add to your existing styles */
.success-icon {
  font-size: 64px;
  text-align: center;
  margin: 20px 0;
}

.success-text {
  color: #4b5563;
  font-size: 16px;
  text-align: center;
  margin: 16px 0;
  line-height: 1.5;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.text-center {
  text-align: center;
}

/* Animation for success state transition */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

#successState:not(.hidden) {
  animation: fadeIn 0.3s ease-in-out;
}

/* Enhanced password requirements */
.password-requirements {
  margin-top: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  font-size: 13px;
}

.requirements-title {
  color: #374151;
  margin-bottom: 12px;
  font-weight: 600;
  font-size: 14px;
}

.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  gap: 8px;
}

.requirements-list li {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #6b7280;
  padding: 6px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.5);
}

.requirements-list li.met {
  color: #059669;
  background-color: rgba(5, 150, 105, 0.1);
  border-left: 3px solid #10b981;
}

.check-icon {
  font-size: 14px;
  min-width: 18px;
  height: 18px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #e5e7eb;
  transition: all 0.3s ease;
}

.requirements-list li.met .check-icon {
  background-color: #10b981;
  color: white;
}

.requirements-list li.met .check-icon::before {
  content: '✓';
  font-weight: bold;
}

.requirements-list li:not(.met) .check-icon::before {
  content: '○';
  color: #9ca3af;
}

/* Dark theme support */
html.theme--dark .password-requirements,
.dark-theme .password-requirements {
  background-color: rgba(255, 255, 255, 0.05);
}

html.theme--dark .requirements-title,
.dark-theme .requirements-title {
  color: #e5e7eb;
}

html.theme--dark .requirements-list li,
.dark-theme .requirements-list li {
  color: #9ca3af;
}

html.theme--dark .requirements-list li.met,
.dark-theme .requirements-list li.met {
  color: #34d399;
}

/* Responsive design improvements */
@media (max-height: 650px) {
  body {
    height: 550px;
    padding: 20px;
  }

  .auth-container {
    padding: 28px 32px;
  }

  .brand {
    margin-bottom: 20px;
  }

  .logo {
    width: 120px;
    margin-bottom: 16px;
  }

  h1 {
    font-size: 28px;
    margin-bottom: 6px;
  }

  .subtitle {
    font-size: 15px;
  }

  .form-container {
    gap: 16px;
  }

  .input-group {
    gap: 12px;
  }

  .password-requirements {
    padding: 12px;
    margin-top: 8px;
  }
}

@media (max-height: 500px) {
  body {
    height: 450px;
    padding: 16px;
  }

  .auth-container {
    padding: 20px 28px;
  }

  .brand {
    margin-bottom: 16px;
  }

  .logo {
    width: 100px;
    margin-bottom: 12px;
  }

  h1 {
    font-size: 24px;
    margin-bottom: 4px;
  }

  .subtitle {
    font-size: 14px;
  }

  .form-container {
    gap: 12px;
  }

  .input-group {
    gap: 10px;
  }

  input {
    padding: 12px 16px;
  }

  .action-button {
    padding: 12px 20px;
  }

  .password-requirements {
    padding: 10px;
    margin-top: 6px;
  }

  .requirements-list {
    gap: 6px;
  }

  .requirements-list li {
    padding: 4px 6px;
  }
}

/* Improved focus management */
input:focus,
button:focus {
  outline: none;
}

/* Better visual feedback for interactive elements */
.link, .action-button, input {
  -webkit-tap-highlight-color: transparent;
}

/* Smooth transitions for all interactive elements */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Ensure proper text rendering */
body, input, button {
  text-rendering: optimizeLegibility;
}
