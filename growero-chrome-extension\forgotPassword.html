<!DOCTYPE html>
<html>
  <head>
    <title>Reset Password - Growero</title>
    <link rel="stylesheet" href="styles.css" />
  </head>
  <body>
    <div class="app-container">
      <div class="auth-container">
        <div class="brand">
          <img
            src="https://res.cloudinary.com/dzptz7ar0/image/upload/v1738612291/growero_logo_d3rjnz.png"
            alt="Growero"
            class="logo"
            style="width: 150px"
          />
          <h1 id="pageTitle">Reset Password</h1>
          <p class="subtitle" id="pageSubtitle">
            Enter your email to reset your password
          </p>
        </div>

        <div class="form-container">
          <form id="forgotPasswordForm" class="space-y-6">
            <div class="input-group">
              <label>Email Address</label>
              <input
                type="email"
                id="forgotPasswordEmail"
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div id="errorMessage" class="error-message hidden"></div>

            <button type="submit" class="action-button" id="sendResetButton">
              <span>Send Reset Email</span>
            </button>

            <div class="bottom-text">
              <span id="backToLogin" class="link">Back to Login</span>
            </div>
          </form>

          <div id="successState" class="text-center space-y-6 hidden">
            <p class="success-text">
              We've sent a password reset link to your email. Please check your
              inbox and follow the instructions.
            </p>
            <button class="action-button" id="backToLoginButton">
              <span>Back to Login</span>
            </button>
          </div>
        </div>
      </div>
    </div>
    <script src="forgotPassword.js"></script>
  </body>
</html>
