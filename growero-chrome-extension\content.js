// Inject ChatGPT button and dropdown
const injectChatGPTButton = () => {
  document
    .querySelectorAll(
      ".comments-comment-texteditor > .display-flex > .display-flex"
    )
    .forEach((el) => {
      if (el.getAttribute("hasChatGPT") === "true") return;
      el.setAttribute("hasChatGPT", "true");

      // wrapper for button and dropdown
      const chatGPTBtnWrapper = document.createElement("div");
      chatGPTBtnWrapper.style.cssText = `
        position: relative;

        display: inline-block;
        z-index: 9999;
      `;

      // chatgpt button with logo
      const chatGPTBtn = document.createElement("button");
      chatGPTBtn.setAttribute("type", "button");
      chatGPTBtn.setAttribute("id", "chatgpt-btn");

      chatGPTBtn.setAttribute(
        "class",
        "artdeco-button--tertiary artdeco-button artdeco-button--circle artdeco-button--muted"
      );

      // button styling
      chatGPTBtn.style.cssText = `
        padding: 6px;
        background: none;
        border: none;
        transition: all 0.2s ease-in-out;

        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 9999;
      `;

      const logoImg = document.createElement("img");
      logoImg.src = "https://app.growero.io/images/short_logo_dark.png";
      logoImg.alt = "Growero Logo";
      logoImg.style.cssText = `
        width: 24px;
        height: 24px;
        transition: transform 0.2s ease-in-out;
        object-fit: contain;
        border-radius: 4px;
      `;

      chatGPTBtn.appendChild(logoImg);

      // this is the dropdown menu with various options
      const dropdown = document.createElement("div");
      dropdown.setAttribute("id", "chatgpt-comment-dropdown");
      dropdown.style.cssText = `

        position: absolute;
        top: 100%;
        left: 0;
        z-index: 99999;
        background-color: #ffffff;
        color: #292929;
        min-width: 250px;
        padding: 8px 0;
        margin-top: 8px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        font-size: 14px;
        display: none;
        opacity: 1;
        transform: none;
      `;

      const options = [
        { type: "positive", icon: "✅", text: "Positive Feedback" },
        { type: "constructive", icon: "🛠️", text: "Constructive Feedback" },
        { type: "question", icon: "❓", text: "Thoughtful Question" },
        { type: "insight", icon: "💡", text: "Add Insight" },
        { type: "experience", icon: "🎯", text: "Share Experience" },
        { type: "congratulate", icon: "🎉", text: "Congratulate" },
        { type: "support", icon: "🤝", text: "Show Support" },
        { type: "collaborate", icon: "🤖", text: "Propose Collaboration" },
        { type: "network", icon: "🌐", text: "Network Building" },
      ];

      dropdown.innerHTML = options
        .map(
          (option) => `
        <div class="dropdown-item" data-type="${option.type}" style="
          padding: 8px 16px;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 8px;
          transition: all 0.2s ease-in-out;
          color: #292929;
          background-color: #ffffff;
        ">
          <span style="font-size: 16px;">${option.icon}</span>
          <span>${option.text}</span>
        </div>
      `
        )
        .join("");

      // toggle dropdown according to theme
      chatGPTBtn.onclick = (e) => {
        e.stopPropagation();
        const isVisible = dropdown.style.display === "block";
        dropdown.style.display = isVisible ? "none" : "block";
      };

      // agar bahar click kiya toh this will close the dropdown
      document.addEventListener("click", (e) => {
        if (!dropdown.contains(e.target) && !chatGPTBtn.contains(e.target)) {
          dropdown.style.display = "none";
        }
      });

      // dropdown option selection
      dropdown.addEventListener("click", async (e) => {
        const item = e.target.closest(".dropdown-item");
        if (!item) return;

        const selectedType = item.getAttribute("data-type");
        if (!selectedType) return;

        dropdown.style.display = "none";

        // find post and comment editor
        const postContainer = el.closest(".feed-shared-update-v2");
        if (!postContainer) return;

        const postContentElement = postContainer.querySelector(
          '.feed-shared-inline-show-more-text span[dir="ltr"]'
        );
        if (!postContentElement) {
          console.warn("Post content not found.");
          return;
        }

        const postContent = postContentElement.innerText;
        const commentInputEl = postContainer.querySelector(".ql-editor");
        //console.log("postContent", postContent)

        if (!commentInputEl) {
          console.warn("Comment text editor not found.");
          return;
        }

        // placeholder while processing
        commentInputEl.setAttribute(
          "data-placeholder",
          "Growero is thinking..."
        );

        chatGPTBtn.setAttribute("disabled", "true");

        // request li_at and csrf tokens from background script

        chrome.runtime.sendMessage(
          { action: "getTokens" },
          async (response) => {
            try {
              // Check for chrome runtime errors
              if (chrome.runtime.lastError) {
                throw new Error(`Extension error: ${chrome.runtime.lastError.message}`);
              }

              // Check if we got a valid response
              if (!response) {
                throw new Error("No response from background script");
              }

              if (response.status === "error") {
                throw new Error(response.message || "Failed to get authentication tokens");
              }

              const liAtToken = response?.liAtToken?.replace(/"/g, '');
              const csrfToken = response?.csrfToken?.replace(/"/g, '') || null;

              // Validate tokens
              if (!liAtToken) {
                throw new Error("LinkedIn authentication required. Please log in to LinkedIn first.");
              }

              //console.log("liAtToken, csrfToken", liAtToken, csrfToken)
              // this is the API call to generate the comment
              const apiBaseUrl = (() => {
                const env = "production"; // change this to production for the live extension (most important)
                const urls = {
                  local: "http://localhost:9090",
                  development: "https://devapp.growero.io",
                  production: "https://api.growero.io",
                };
                return urls[env] || urls.production;
              })();

              const apiResponse = await fetch(
                `${apiBaseUrl}/api/extension/generate-comment`,
                {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                    Accept: "application/json",
                    "User-Agent": "Chrome Extension",
                  },
                  body: JSON.stringify({
                    postContent,
                    type: selectedType,
                    li_at_cookie: liAtToken,
                    csrf_token: csrfToken,
                  }),
                }
              );

              //console.log("apiResponse", apiResponse)

              if (!apiResponse.ok) {
                const errorData = await apiResponse.json().catch(() => ({}));
                const errorMessage = errorData.message || `HTTP ${apiResponse.status}: ${apiResponse.statusText}`;
                throw new Error(errorMessage);
              }

              const responseData = await apiResponse.json();
              const comment = responseData?.comment?.comment || responseData?.comment || "Failed to generate comment.";

              commentInputEl.innerHTML = comment;

            } catch (error) {
              console.error("Error fetching comment:", error);

              let errorMessage = "Error generating comment.";
              if (error.message.includes("LinkedIn authentication")) {
                errorMessage = "Please log in to LinkedIn first.";
              } else if (error.message.includes("Failed to fetch")) {
                errorMessage = "Connection error. Please check your internet connection.";
              } else if (error.message.includes("401")) {
                errorMessage = "Authentication failed. Please refresh and try again.";
              } else if (error.message.includes("403")) {
                errorMessage = "Access denied. Please check your permissions.";
              } else if (error.message.includes("500")) {
                errorMessage = "Server error. Please try again later.";
              }

              commentInputEl.innerHTML = errorMessage;
            }

            } catch (outerError) {
              console.error("Outer error in message callback:", outerError);
              commentInputEl.innerHTML = "Extension error. Please try again.";
            } finally {
              commentInputEl.setAttribute("data-placeholder", "Add a comment...");
              chatGPTBtn.removeAttribute("disabled");
            }
          }
        );
      });

      const items = dropdown.querySelectorAll(".dropdown-item");

      items.forEach((item) => {
        item.addEventListener("mouseenter", () => {
          item.style.backgroundColor = "#f3f3f3";
          item.style.color = "#292929";
        });
        item.addEventListener("mouseleave", () => {
          item.style.backgroundColor = "#ffffff";
          item.style.color = "#292929";
        });
      });

      chatGPTBtnWrapper.appendChild(chatGPTBtn);
      chatGPTBtnWrapper.appendChild(dropdown);
      el.prepend(chatGPTBtnWrapper);
    });
};

const style = document.createElement("style");
style.textContent = `
  #chatgpt-btn:hover {
    background-color: #f3f3f3 !important;
  }

  #chatgpt-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .dropdown-item {
    color: #292929 !important;
    background-color: #ffffff !important;
  }

  .dropdown-item:hover {
    background-color: #f3f3f3 !important;
    color: #292929 !important;
  }

  /* Dark theme styles */
  html.theme--dark #chatgpt-comment-dropdown,
  .dark-theme #chatgpt-comment-dropdown {
    background-color: #1a1a1a !important;
    border-color: #404040 !important;
    color: #ffffff !important;
  }

  html.theme--dark .dropdown-item,
  .dark-theme .dropdown-item {
    color: #ffffff !important;
    background-color: #1a1a1a !important;
  }

  html.theme--dark .dropdown-item:hover,
  .dark-theme .dropdown-item:hover {
    background-color: #2d2d2d !important;
    color: #ffffff !important;
  }
`;
document.head.appendChild(style);

// initialize observer
const observer = new MutationObserver(injectChatGPTButton);
observer.observe(document.body, { childList: true, subtree: true });
