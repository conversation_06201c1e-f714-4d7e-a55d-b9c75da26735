<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Growero</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo {
            width: 120px;
            height: auto;
            margin-bottom: 20px;
        }

        h1 {
            color: #0a66c2;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            font-size: 1.2em;
            margin-bottom: 30px;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .feature-card h3 {
            color: #0a66c2;
            margin-top: 0;
        }

        .getting-started {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .steps {
            list-style-type: none;
            padding: 0;
        }

        .steps li {
            margin-bottom: 15px;
            padding-left: 30px;
            position: relative;
        }

        .steps li:before {
            content: "→";
            position: absolute;
            left: 0;
            color: #0a66c2;
        }

        .footer {
            text-align: center;
            color: #666;
            margin-top: 40px;
        }

        .footer a {
            color: #0a66c2;
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="faviconV2.png" alt="Growero Logo" class="logo">
            <h1>Welcome to Growero</h1>
            <p class="subtitle">Unlock LinkedIn's potential with AI-powered engagement</p>
        </div>

        <div class="features">
            <div class="feature-card">
                <h3>AI-Powered Comments</h3>
                <p>Generate contextual, engaging comments on LinkedIn posts with just one click using our advanced AI technology.</p>
            </div>
            <div class="feature-card">
                <h3>Smart Analytics</h3>
                <p>Track your engagement metrics and optimize your LinkedIn presence with detailed insights.</p>
            </div>
            <!-- <div class="feature-card">
                <h3>Time-Saving</h3>
                <p>Automate your engagement while maintaining authenticity and professional standards.</p>
            </div> -->
        </div>

        <div class="getting-started">
            <h2>Getting Started</h2>
            <ul class="steps">
                <li>Click on the Growero extension icon in your browser</li>
                <li>Allow access to LinkedIn by clicking "Allow Access"</li>
                <li>Navigate to any LinkedIn post</li>
                <li>Look for the Growero icon in the comment section</li>
                <li>Select your preferred comment type and let AI do the rest!</li>
            </ul>
        </div>

        <div class="footer">
            <p>Need help? Visit our <a href="https://growero.io/support" target="_blank">Support Center</a></p>
            <p>
                <a href="https://growero.io/privacy-policy" target="_blank">Privacy Policy</a> | 
                <a href="https://growero.io/terms-of-service" target="_blank">Terms of Service</a>
            </p>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const logoImg = document.querySelector('.logo');
        logoImg.src = chrome.runtime.getURL('faviconV2.png');
    });
    </script>
</body>
</html>


<!-- https://social-comments-gpt.com/extension/welcome -->