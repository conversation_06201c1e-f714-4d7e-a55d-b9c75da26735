<!DOCTYPE html>
<html>
  <head>
    <title>Growero Extension</title>
    <link rel="stylesheet" href="styles.css" />
  </head>
  <body>
    <div class="app-container">
      <!-- login h ye -->
      <div class="auth-container" id="loginContainer">
        <div class="brand">
          <img
            src="https://res.cloudinary.com/dzptz7ar0/image/upload/v1738612291/growero_logo_d3rjnz.png"
            alt="Growero"
            class="logo"
            style="width: 150px"
          />
          <h1>Welcome Back</h1>
          <p class="subtitle">Login with email</p>
        </div>

        <div class="form-container">
          <div class="input-group">
            <label for="loginEmail">Email Address</label>
            <input
              type="email"
              placeholder="<EMAIL>"
              id="loginEmail"
              autocomplete="email"
              required
              aria-describedby="email-error"
            />
            <label for="loginPassword">Password</label>
            <input
              type="password"
              placeholder="Enter your password"
              id="loginPassword"
              autocomplete="current-password"
              required
              aria-describedby="password-error"
            />
          </div>
          <div
            class="form-group"
            style="text-align: right; margin-bottom: 16px"
          >
            <a href="#" id="forgot-password-link" class="link-text"
              >Forgot Password?</a
            >
          </div>
          <button class="action-button" id="loginButton" type="submit" aria-describedby="login-status">
            <span>Sign In</span>
          </button>

          <div class="bottom-text">
            <span id="toRegister" class="link"
              >Don't have an account? Create one</span
            >
          </div>
        </div>
      </div>

      <!-- register h ye -->
      <div class="auth-container hidden" id="registerContainer">
        <div class="brand">
          <img
            src="https://res.cloudinary.com/dzptz7ar0/image/upload/v1738612291/growero_logo_d3rjnz.png"
            alt="Growero"
            class="logo"
            style="width: 150px"
          />
          <h1>Create an Account</h1>
          <p class="subtitle">
            Sign up now to start your journey with Growero!
          </p>
        </div>

        <!-- this is the 1st step of registration -->
        <div class="form-container" id="registerStep1">
          <div class="input-group">
            <label for="registerEmail">Email Address</label>

            <input
              type="email"
              placeholder="<EMAIL>"
              id="registerEmail"
              autocomplete="email"
              required
              aria-describedby="register-email-error"
            />
          </div>
          <button class="action-button" id="nextStepButton" type="button">
            <span>Continue</span>
          </button>

          <div class="bottom-text">
            <span id="toLogin" class="link"
              >Already have an account? Sign In</span
            >
          </div>
        </div>

        <!-- this is the 2nd step of registration -->
        <div class="form-container hidden" id="registerStep2">
          <div class="input-group">
            <label for="registerPassword">Create Password</label>

            <input
              type="password"
              placeholder="Create a strong password"
              id="registerPassword"
              autocomplete="new-password"
              required
              aria-describedby="password-requirements"
            />
            <div class="password-requirements">
              <p class="requirements-title">Password Requirements:</p>
              <ul class="requirements-list">
                <li id="req-length">
                  <span class="check-icon"></span> At least 8 characters
                </li>
                <li id="req-uppercase">
                  <span class="check-icon"></span> Upper case letters (A-Z)
                </li>
                <li id="req-lowercase">
                  <span class="check-icon"></span> Lower case letters (a-z)
                </li>
                <li id="req-number">
                  <span class="check-icon"></span> Numbers (0-9)
                </li>
                <li id="req-special">
                  <span class="check-icon"></span> Special characters (e.g.
                  !@#$%^&*)
                </li>
              </ul>
            </div>
          </div>
          <button class="action-button" id="registerButton">
            <span>Sign Up</span>
          </button>
          <!-- <button class="action-button secondary" id="backToEmailButton">
            <span>Back</span>
          </button> -->
        </div>

        <!-- this is the 3rd step of registration -->
        <div class="form-container hidden" id="registerStep3">
          <div class="input-group">
            <label class="verify-text">Verify Your Email</label>
            <p class="verify-text">
              We've sent a verification link to your email address. Please check
              your inbox and click the link to activate your account.
            </p>
          </div>
          <button class="action-button" id="backToLoginFromVerify">
            <span>Back to Login</span>
          </button>
        </div>
      </div>
    </div>
    <script src="popup.js"></script>
  </body>
</html>
