/* eslint-disable no-undef */
/* eslint-disable no-unused-vars */
document.addEventListener("DOMContentLoaded", function () {
  const loginContainer = document.getElementById("loginContainer");
  const registerContainer = document.getElementById("registerContainer");
  const toRegister = document.getElementById("toRegister");
  const toLogin = document.getElementById("toLogin");
  const loginButton = document.getElementById("loginButton");
  const registerButton = document.getElementById("registerButton");
  const loginError = document.getElementById("loginError");
  const loginEmail = document.getElementById("loginEmail");
  const loginPassword = document.getElementById("loginPassword");

  const BASE_URL = 'https://api.growero.io/api/';
  //const BASE_URL = "http://localhost:9090/api/";

  // debug logging
  console.log("Elements found:", {
    loginContainer: !!loginContainer,
    registerContainer: !!registerContainer,
    toRegister: !!toRegister,
    toLogin: !!toLogin,
    loginButton: !!loginButton,
    registerButton: !!registerButton,
    loginError: !!loginError,
    loginEmail: !!loginEmail,
    loginPassword: !!loginPassword,
  });

  // check karo if required elements exist
  if (
    !loginContainer ||
    !registerContainer ||
    !toRegister ||
    !toLogin ||
    !loginButton
  ) {
    console.error("Required elements not found");
    return;
  }

  function safeClosePopup() {
    try {
      window.close();
    } catch (error) {
      setTimeout(() => {
        window.open("", "_self").close();
      }, 0);
    }
  }

  function showError(message) {
    const email = registerEmail.value.trim();
    const password = registerPassword.value;

    if (message.includes("email")) {
      registerEmail.classList.add("error");
    }
    if (message.includes("password")) {
      registerPassword.classList.add("error");
    }
  }

  function clearError() {
    registerEmail?.classList.remove("error");
    registerPassword?.classList.remove("error");
    loginEmail?.classList.remove("error");
    loginPassword?.classList.remove("error");
  }

  function setLoading(button, isLoading) {
    if (!button) return;
    button.disabled = isLoading;
    const buttonText = button.querySelector("span");
    if (buttonText) {
      buttonText.textContent = isLoading ? "Processing..." : "Sign In";
    }
    button.classList.toggle("loading", isLoading);
  }

  function switchContainer(hideContainer, showContainer) {
    if (!hideContainer || !showContainer) {
      console.error("Container missing:", { hideContainer, showContainer });
      return;
    }
    console.log("Switching containers:", {
      from: hideContainer.id,
      to: showContainer.id,
    });

    hideContainer.classList.add("hidden");
    showContainer.classList.remove("hidden");
    clearError();
  }

  toRegister.addEventListener("click", () => {
    loginContainer.classList.add("hidden");
    registerContainer.classList.remove("hidden");
  });

  toLogin.addEventListener("click", () => {
    registerContainer.classList.add("hidden");
    loginContainer.classList.remove("hidden");
  });

  async function safeFetch(url, options) {
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          "Content-Type": "application/json",
          ...options.headers,
        },
      });
      return response;
    } catch (error) {
      console.error("Fetch error:", error);
      throw new Error("Network error occurred");
    }
  }

  // ye login k liye
  loginButton.addEventListener("click", async (e) => {
    e.preventDefault();
    clearError();

    const email = loginEmail.value;
    const password = loginPassword.value;

    let hasError = false;

    if (!email || !isValidEmail(email)) {
      loginEmail.classList.add("error");
      hasError = true;
    }

    if (!password) {
      loginPassword.classList.add("error");
      hasError = true;
    }

    if (hasError) return;

    // dont allow button during login
    setLoading(loginButton, true);

    try {
      const response = await fetch(`${BASE_URL}/userprofile/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({ email, password }),
        credentials: "include",
        mode: "cors",
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const data = await response.json();

      if (data.authenticated) {
        // store token in extension storage
        chrome.storage.local.set(
          {
            token: data.token,
            userInfo: data.user,
          },
          () => {
            if (chrome.runtime.lastError) {
              console.error("Storage error:", chrome.runtime.lastError);
              showError("Failed to save login information");
              return;
            }

            window.location.href = "main.html";
          }
        );
      } else {
        throw new Error("Authentication failed");
      }
    } catch (error) {
      console.error("Login error:", error);

      let errorMessage = "An error occurred during login";
      if (error.message.includes("Failed to fetch")) {
        errorMessage = "problem h kuch check it";
      } else if (error.message.includes("401")) {
        errorMessage = "Invalid email or password";
      } else if (error.message.includes("403")) {
        errorMessage = "Access denied. Please try again.";
      } else if (error.message !== "Authentication failed") {
        errorMessage = error.message;
      }

      showError(errorMessage);
      loginPassword.value = ""; // ye password clear karega on error
    } finally {
      setLoading(loginButton, false);
    }
  });

  // validation h for password
  const registerPassword = document.getElementById("registerPassword");
  const requirements = {
    length: document.getElementById("req-length"),
    uppercase: document.getElementById("req-uppercase"),
    lowercase: document.getElementById("req-lowercase"),
    number: document.getElementById("req-number"),
    special: document.getElementById("req-special"),
  };

  function validatePassword(password) {
    const checks = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    };

    // update requirement icons
    Object.keys(checks).forEach((check) => {
      const li = requirements[check];
      if (li) {
        const icon = li.querySelector(".check-icon");
        if (checks[check]) {
          icon.textContent = "";
          li.classList.add("met");
        } else {
          icon.textContent = "";
          li.classList.remove("met");
        }
      }
    });

    // return true if all requirements are met
    return Object.values(checks).every(Boolean);
  }

  // add input listener to password field
  registerPassword?.addEventListener("input", (e) => {
    validatePassword(e.target.value);
  });

  // add these after your existing event listeners
  const registerStep1 = document.getElementById("registerStep1");
  const registerStep2 = document.getElementById("registerStep2");
  const registerStep3 = document.getElementById("registerStep3");
  const nextStepButton = document.getElementById("nextStepButton");
  const backToEmailButton = document.getElementById("backToEmailButton");
  const backToLoginFromVerify = document.getElementById("backToLoginFromVerify");


  // function to validate email
  function isValidEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }

  // handle next step button click
  nextStepButton?.addEventListener("click", (e) => {
    e.preventDefault();
    clearError();

    const email = registerEmail.value.trim();
    if (!email || !isValidEmail(email)) {
      registerEmail.classList.add("error");
      return;
    }

    // switch to password step
    registerStep1.classList.add("hidden");
    registerStep2.classList.remove("hidden");
  });

  // handle back button click
  backToEmailButton?.addEventListener("click", (e) => {
    e.preventDefault();

    clearError();

    // switch back to email step
    registerStep2.classList.add("hidden");
    registerStep1.classList.remove("hidden");
  });

  // Update the register button click handler
  registerButton.addEventListener("click", async (e) => {
    e.preventDefault();
    clearError();

    const email = registerEmail.value.trim();
    const password = registerPassword.value;

    let hasError = false;

    if (!email || !isValidEmail(email)) {
      registerEmail.classList.add("error");
      hasError = true;
    }

    if (!password) {
      registerPassword.classList.add("error");
      hasError = true;
    }

    if (!validatePassword(password)) {
      registerPassword.classList.add("error");
      hasError = true;
    }

    if (hasError) return;

    // this is step 3 after validation
    registerStep2.classList.add("hidden");
    registerStep3.classList.remove("hidden");

    const verifyText = registerStep3.querySelector(".verify-text");
    if (verifyText) {
      verifyText.textContent = `We've sent a verification link to ${email}. Please check your inbox and click the link to activate your account.`;
    }

    setLoading(registerButton, true);

    try {
      const response = await fetch(`${BASE_URL}/userprofile/register`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({ email, password }),
        credentials: "include",
        mode: "cors",
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      await response.json();

    } catch (error) {
      console.error("Register error:", error);
      let errorMessage = "An error occurred during registration";
      
      if (error.message.includes("Failed to fetch")) {
        errorMessage = "Connection error. Please try again.";
      } else if (error.message.includes("401")) {
        errorMessage = "Invalid email or password";
      } else if (error.message.includes("403")) {
        errorMessage = "Access denied. Please try again.";
      } else if (error.message !== "Authentication failed") {
        errorMessage = error.message;
      }

      showError(errorMessage);
      registerPassword.value = "";
    } finally {
      setLoading(registerButton, false);
    }
  });

  // step 3
  backToLoginFromVerify?.addEventListener("click", () => {
    registerContainer.classList.add("hidden");
    loginContainer.classList.remove("hidden");
    
    registerEmail.value = "";
    registerPassword.value = "";
    registerStep3.classList.add("hidden");
    registerStep1.classList.remove("hidden");
    
    document.querySelectorAll('.requirements-list li').forEach(li => {
      li.classList.remove('met');
      const icon = li.querySelector('.check-icon');
      if (icon) icon.textContent = "";
    });
  });

  // modify the check for already logged in user
  chrome.storage.local.get(["token"], function (result) {
    if (result.token) {
      // instead of closing, redirect to main.html
      window.location.href = "main.html";
    }
  });

  const forgotPasswordLink = document.getElementById("forgot-password-link");

  forgotPasswordLink?.addEventListener("click", (e) => {
    e.preventDefault();
    window.location.href = "forgotPassword.html";
  });

  const style = document.createElement("style");
  style.textContent = `

    .link-text {
      color: #0a66c2;
      text-decoration: none;
      font-size: 14px;
      transition: color 0.2s;
    }

    .link-text:hover {
      color: #004182;
      text-decoration: underline;
    }

    .success-message {
      animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  `;
  document.head.appendChild(style);

  [registerEmail, registerPassword, loginEmail, loginPassword].forEach(
    (input) => {
      input?.addEventListener("input", () => {
        input.classList.remove("error");
      });
    }
  );
});
