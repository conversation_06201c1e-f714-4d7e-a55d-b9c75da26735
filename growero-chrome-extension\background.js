let authToken = null;
let apiEndpoint = null;
let currentEnv = "production";

// Load Config File
chrome.runtime.onInstalled.addListener(() => {
  console.log("Extension installed or updated.");

  // Remove any existing rules first
  chrome.declarativeNetRequest.getDynamicRules().then(rules => {
    const ruleIds = rules.map(rule => rule.id);

    if (ruleIds.length > 0) {
      chrome.declarativeNetRequest.updateDynamicRules({
        removeRuleIds: ruleIds,
        addRules: []
      });
    }

    // Add new rules
    chrome.declarativeNetRequest.updateDynamicRules({
      addRules: [
        {
          id: 1001, // Using a higher ID to avoid conflicts
          priority: 1,
          action: {
            type: "modifyHeaders",
            requestHeaders: [
              { header: "Origin", operation: "set", value: "https://www.linkedin.com" }
            ]
          },
          condition: {
            urlFilter: "*",
            resourceTypes: ["main_frame", "sub_frame", "xmlhttprequest"]
          }
        },
        {
          id: 1002, // Using a higher ID to avoid conflicts
          priority: 1,
          action: {
            type: "modifyHeaders",
            responseHeaders: [
              { header: "content-security-policy", operation: "remove" },
              { header: "x-frame-options", operation: "remove" }
            ]
          },
          condition: {
            urlFilter: "*",
            resourceTypes: ["main_frame", "sub_frame"]
          }
        }
      ]
    }).catch(error => {
      console.error("Error setting declarativeNetRequest rules:", error);
    });
  });

  // Reset auth token on installation
  chrome.storage.local.set({ authToken: null });

  // Load config file
  fetch(chrome.runtime.getURL("config.json"))
    .then((response) => response.json())
    .then((config) => {
      apiEndpoint = config[currentEnv].apiEndpoint;
      //console.log("Loaded API Endpoint:", apiEndpoint);
    })
    .catch((error) => console.error("Error loading config file:", error));
});

// Listener for Messages from Content Scripts or Popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "setAuthToken") {
    authToken = request.token;
    chrome.storage.local.set({ authToken });
    sendResponse({ success: true, message: "Token stored successfully" });
  } else if (request.action === "getAuthToken") {
    chrome.storage.local.get("authToken", (data) => {
      sendResponse({ authToken: data.authToken || null });
    });
    return true;
  } else if (request.action === "apiRequest") {
    handleApiRequest(request.endpoint, request.method, request.data)
      .then((response) => sendResponse(response))
      .catch((error) => sendResponse({ success: false, error: error.message }));
    return true;
  } else if (request.action === "loadConfig") {
    // Added method to reload config
    fetch(chrome.runtime.getURL("config.json"))
      .then((response) => response.json())
      .then((config) => {
        apiEndpoint = config[currentEnv].apiEndpoint;
        sendResponse({ success: true, apiEndpoint });
      })
      .catch((error) => {
        console.error("Error loading config file:", error);
        sendResponse({ success: false, error: error.message });
      });
    return true;
  } else if (request.action === "getTokens") {
    getCookiesFromLinkedInTemp()
    .then(cookieData => sendResponse(cookieData))
    .catch(error => sendResponse({ status: "error", message: error.message }));
    return true;
  } else if (request.action === "getCookies") {
    getCookiesFromLinkedIn()
      .then(cookieData => sendResponse(cookieData))
      .catch(error => sendResponse({ status: "error", message: error.message }));
    return true; // Important to return true for async response
  }

});

async function getCookiesFromLinkedInTemp() {
  try {
    const liAtCookie = await chrome.cookies.get({
      url: "https://www.linkedin.com",
      name: "li_at"
    });

    const jsessionidCookie = await chrome.cookies.get({
      url: "https://www.linkedin.com",
      name: "JSESSIONID"
    });

    return {
      liAtToken: liAtCookie?.value,
      csrfToken: jsessionidCookie?.value
    };
  } catch (error) {
    console.error("Error getting cookies:", error);
    return { status: "error", message: error.message };
  }
}


async function getCookiesFromLinkedIn() {
  try {
    const liAtCookie = await chrome.cookies.get({
      url: "https://www.linkedin.com",
      name: "li_at"
    });

    const jsessionidCookie = await chrome.cookies.get({
      url: "https://www.linkedin.com",
      name: "JSESSIONID"
    });

    if (!liAtCookie || !liAtCookie.value) {
      return { status: "error", message: "Could not find li_at cookie. Please log in to LinkedIn." };
    }

    // Call API to update tokens, but don't pass an endpoint since it's already in the URL
    try {
      const apiResponse = await handleApiRequest('', 'POST', {
        li_at_cookie: liAtCookie.value,
        csrf_token: jsessionidCookie ? jsessionidCookie.value : null
      });

      return {
        status: "success",
        apiResponse: {
          li_at_cookie: liAtCookie.value,
          csrf_token: jsessionidCookie ? jsessionidCookie.value : null
        }
      };
    } catch (apiError) {
      console.error("API Error:", apiError);
      return { status: "error", message: "Failed to update tokens in database: " + apiError.message };
    }
  } catch (error) {
    console.error("Error getting cookies:", error);
    return { status: "error", message: error.message };
  }
}


// Function to Handle API Requests
async function handleApiRequest(endpoint = "", method = "GET", data = null) {
  try {
    // Check if apiEndpoint is defined, if not, try to load it
    if (!apiEndpoint) {
      try {
        const configResponse = await fetch(chrome.runtime.getURL("config.json"));
        const config = await configResponse.json();
        apiEndpoint = config[currentEnv].apiEndpoint;
      } catch (configError) {
        throw new Error("Failed to load API endpoint configuration");
      }
    }

    const headers = { "Content-Type": "application/json" };
    if (authToken) headers["Authorization"] = `Bearer ${authToken}`;

    // Don't append endpoint since it's already in the apiEndpoint config
    const response = await fetch(`${apiEndpoint}`, {
      method,
      headers,
      body: data ? JSON.stringify(data) : null,
    });

    if (!response.ok) throw new Error(`API Error: ${response.status}`);
    return await response.json();
  } catch (error) {
    console.error("API Request Error:", error);
    return { success: false, error: error.message };
  }
}
